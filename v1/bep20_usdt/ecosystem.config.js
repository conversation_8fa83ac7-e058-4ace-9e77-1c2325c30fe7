const {
  calcPath,
  getEnvVariables
} = require('./helpers');

module.exports = {
  // 全局配置
  node_version: "16.20.1",
  apps: [

    {
      name: "v1/bep20-usdt/getLogs",
      cwd: calcPath('src/workers'),
      script: "getLogs.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/bep20-usdt/addRepeatJob",
      cwd: calcPath('src/workers'),
      script: "addRepeatJob.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/bep20-usdt/checkLogs_bep20_usdt_data",
      cwd: calcPath('src/workers'),
      script: "checkLogs_bep20_usdt_data.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/bep20-usdt/incrNum",
      cwd: calcPath('src/workers'),
      script: "incrNum.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/bep20-usdt/updateTokenBalance",
      cwd: calcPath('src/workers'),
      script: "updateTokenBalance.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    {
      name: "v1/bep20-usdt/ui",
      cwd: calcPath('src'),
      script: "ui.js",
      exec_mode: "fork",
      kill_timeout: 60000,
      args: [],
      env: getEnvVariables()
    },

    // {
    //   name: "tmp/getaddress",
    //   cwd: calcPath('src/workers'),
    //   script: "getaddress.js",
    //   exec_mode: "fork",
    //   kill_timeout: 60000,
    //   args: [],
    //   env: getEnvVariables()
    // },

    // {
    //   name: "tmp/addAddressRep",
    //   cwd: calcPath('src/workers'),
    //   script: "addAddressRep.js",
    //   exec_mode: "fork",
    //   kill_timeout: 60000,
    //   args: [],
    //   env: getEnvVariables()
    // },
    // {
    //   name: "tmp/tmpUpdateBalance",
    //   cwd: calcPath('src/workers'),
    //   script: "tmpUpdateBalance.js",
    //   exec_mode: "fork",
    //   kill_timeout: 60000,
    //   args: [],
    //   env: getEnvVariables()
    // },

  ]
}