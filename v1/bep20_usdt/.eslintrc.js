module.exports = {
    env: {
        node: true,
        es2021: true,
        jest: true
    },
    extends: [
        'eslint:recommended',
        'plugin:node/recommended'
    ],
    parserOptions: {
        ecmaVersion: 12,
        sourceType: 'module'
    },
    plugins: [
        'node'
    ],
    rules: {
        // 代码风格
        'indent': ['error', 4],
        'linebreak-style': ['error', 'unix'],
        'quotes': ['error', 'single'],
        'semi': ['error', 'always'],
        
        // 变量
        'no-unused-vars': ['warn', { 
            'argsIgnorePattern': '^_',
            'varsIgnorePattern': '^_'
        }],
        'no-undef': 'error',
        
        // 最佳实践
        'eqeqeq': 'error',
        'no-eval': 'error',
        'no-implied-eval': 'error',
        'no-new-func': 'error',
        'no-script-url': 'error',
        
        // ES6+
        'prefer-const': 'error',
        'no-var': 'error',
        'prefer-arrow-callback': 'warn',
        
        // Node.js 特定
        'node/no-unpublished-require': 'off',
        'node/no-missing-require': 'error',
        'node/no-extraneous-require': 'error',
        'node/no-unsupported-features/es-syntax': ['error', {
            'version': '>=16.20.1',
            'ignores': []
        }],

        // 错误处理
        'no-console': 'off', // 允许console，因为这是服务器应用
        'handle-callback-err': 'error'
    },
    overrides: [
        {
            files: ['tests/**/*.js', '**/*.test.js'],
            env: {
                jest: true
            },
            rules: {
                'node/no-unpublished-require': 'off'
            }
        }
    ]
};
