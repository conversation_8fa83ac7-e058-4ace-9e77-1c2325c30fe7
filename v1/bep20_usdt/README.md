# BEP20 USDT 监控系统

一个用于监控 Binance Smart Chain 上 BEP20 USDT 代币转账的 Node.js 应用程序。

## 系统要求

### Node.js 版本
- **生产环境**: Node.js v16.20.1 或更高版本
- **推荐版本**: Node.js v16.20.1

### 版本管理
项目包含以下版本配置文件：
- `.nvmrc` - 指定 Node.js 版本为 16.20.1
- `package.json` - engines 字段指定最低版本要求
- `ecosystem.config.js` - PM2 配置中指定 Node.js 版本

### 使用 NVM 切换版本
```bash
# 安装并使用指定的 Node.js 版本
nvm install
nvm use

# 或者直接指定版本
nvm use 16.20.1
```

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
复制 `.env.example` 到 `.env` 并配置相应的环境变量。

### 3. 运行测试
```bash
npm test
```

### 4. 启动应用
```bash
# 开发环境
npm start

# 生产环境 (使用 PM2)
pm2 start ecosystem.config.js
```

## 架构概述

### 核心组件
- **Worker 进程**: 处理区块链日志监控和数据处理
- **Web UI**: 提供监控界面和管理功能
- **队列系统**: 使用 BullMQ Pro 处理异步任务
- **数据存储**: MySQL 数据库 + Redis 缓存

### Worker 进程
- `getLogs.js` - 获取区块链日志
- `checkLogs_bep20_usdt_data.js` - 处理 USDT 转账数据
- `updateTokenBalance.js` - 更新代币余额
- `incrNum.js` - 增量数据处理
- `addRepeatJob.js` - 添加重复任务

## 代码质量

### ESLint 配置
项目配置了严格的 ESLint 规则，确保代码质量：
- Node.js v16.20.1+ 兼容性检查
- 禁用 `process.exit()` 使用，改用错误抛出
- 统一的代码风格和最佳实践

### 测试覆盖
- 单元测试覆盖核心功能
- 配置测试、日志系统、错误处理测试
- 使用 Jest 测试框架

## 部署说明

### 生产环境要求
1. Node.js v16.20.1+
2. MySQL 数据库
3. Redis 服务器
4. PM2 进程管理器

### 环境变量配置
确保生产环境中正确配置所有必需的环境变量，特别是：
- 数据库连接信息
- Redis 连接信息
- 区块链 RPC 端点
- 安全密钥

## 维护和监控

### 日志系统
- 结构化日志记录
- 多级别日志支持 (ERROR, WARN, INFO, DEBUG)
- 文件和控制台输出

### 错误处理
- 自定义错误类型
- 统一错误处理机制
- 优雅的进程关闭

### 性能监控
- 数据库连接池优化
- Redis 连接管理
- 内存使用监控

## 许可证

ISC License
