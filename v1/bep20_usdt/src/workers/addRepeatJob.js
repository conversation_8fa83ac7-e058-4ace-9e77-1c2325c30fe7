const {
    ethers
} = require("ethers");

const fs = require("fs");

const moment = require('moment');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection10 = require('../config/redis_config')(10);

const {
    coin
} = require('../config/coin_config');

const block_track_Queue = new QueuePro(`${coin}-block-track`, {
    connection: connection10
});

async function main() {
    
    await block_track_Queue.removeRepeatableByKey('repeatBlock::::2000');

    block_track_Queue.add('repeatBlock', {}, {
        repeat: {
            every: 2000,
        },
        attempts: 10,
        backoff: {
            type: 'exponential',
            delay: 1000,
        },
        removeOnComplete: 1000,
        removeOnFail: 1000
    });
}

main();


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await block_track_Queue.close();
            throw new Error(`Process ${type} handled successfully`);
        } catch (error) {
            throw new Error(`Failed to handle process ${type}: ${error.message}`);
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await block_track_Queue.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});