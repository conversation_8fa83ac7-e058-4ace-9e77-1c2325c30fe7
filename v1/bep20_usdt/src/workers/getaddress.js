const {
    ethers
} = require("ethers");

const moment = require('moment');

const Redis = require("ioredis");



const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection19 = require('../config/redis_config')(19);

const updateBep20UsdtBalanceQueue = new QueuePro(`updateBep20UsdtBalanceQueueProTmp`, {
    connection: connection19
});


const redis11 = new Redis(connection19);

const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database
    }
});



const worker = new WorkerPro(`addr-track`, async (job) => {

    let timp_address_all_lastId = await redis11.get('timp_address_all_lastId');
    if (timp_address_all_lastId) {
        let addrs = await knex.column('id', 'address', 'ipn_url', 'label').from('address_all').where('id', '>', timp_address_all_lastId).select().limit(100);
        for (let i = 0; i < addrs.length; i++) {
            await updateBep20UsdtBalanceQueue.add('hhsd',{
                address: addrs[i].address,
                field: 'bep20-usdt',
                table: 'address_all',
                length:addrs.length
            });
            await redis11.set('timp_address_all_lastId', addrs[i].id);
        }
    } else {
        await redis11.set('timp_address_all_lastId', 1);
    }

}, {
    connection: connection19
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue trc20_block_track`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("error", (err) => {
    console.log(`error job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});


worker.on('closed', () => {
    console.error(`worker  已退出`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            throw new Error(`Process ${type} handled successfully`);
        } catch (error) {
            throw new Error(`Failed to handle process ${type}: ${error.message}`);
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});