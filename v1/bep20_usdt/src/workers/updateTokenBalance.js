const connection10 = require('../config/redis_config')(10);
const { createLogger } = require('../utils/logger');
const {
    handleDatabaseOperation,
    handleBlockchainOperation,
    validateRequired,
    validateAddress,
    setupProcessErrorHandlers
} = require('../utils/errorHandler');

const {
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const {
    ethers
} = require("ethers");

const logger = createLogger('UPDATE_TOKEN_BALANCE');

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);

const QueueName = "updateBep20UsdtBalanceQueuePro";

const mysqlConfig = require('../config/mysqlConfig');

const knex = require('knex')({
    client: 'mysql2',
    connection: {
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        port: mysqlConfig.port,
        password: mysqlConfig.password,
        database: mysqlConfig.database,
        charset: 'utf8mb4',
        timezone: '+08:00'
    },
    pool: {
        min: 1,
        max: 5,
        acquireTimeoutMillis: 30000,
        createTimeoutMillis: 30000,
        destroyTimeoutMillis: 5000,
        idleTimeoutMillis: 30000,
        reapIntervalMillis: 1000,
        createRetryIntervalMillis: 100
    },
    acquireConnectionTimeout: 30000
});

const worker = new WorkerPro(QueueName, async (job) => {
    const startTime = Date.now();
    let address = 'unknown';

    try {
        const { address: jobAddress, table, field, contractAddress, abi, decimals } = job.data;
        address = jobAddress;

        // 验证必需参数
        validateRequired(job.data, ['address', 'table', 'field', 'contractAddress', 'abi']);
        validateAddress(address);

        if (decimals === undefined) {
            throw new ValidationError('Decimals parameter is required');
        }

        logger.info(`Starting balance update for address: ${address}`, { table, field });

        // 获取区块链余额
        const balance = await handleBlockchainOperation(async () => {
            const bep20 = new ethers.Contract(contractAddress, abi, provider);
            const balanceCoin = await bep20.balanceOf(address);
            return ethers.utils.formatUnits(String(balanceCoin), decimals);
        }, 'Get balance from blockchain');

        // 更新数据库
        const result = await handleDatabaseOperation(async () => {
            const updateData = { [field]: balance };
            if (table === 'w_bep20_address') {
                updateData.ck_conletion = 0;
            }

            const updateResult = await knex(table)
                .update(updateData)
                .where({ address: address });

            if (updateResult === 0) {
                logger.warn(`No rows updated for address ${address} in table ${table}`);
            }

            return updateResult;
        }, 'Update balance in database');

        const duration = Date.now() - startTime;
        logger.info(`Balance update completed for ${address}`, {
            balance,
            table,
            duration: `${duration}ms`,
            rowsAffected: result
        });

        return result;
    } catch (error) {
        const duration = Date.now() - startTime;
        logger.error(`Failed to update token balance for ${address}`, {
            duration: `${duration}ms`,
            jobData: job.data
        }, error);
        throw error; // 重新抛出错误以便BullMQ处理重试
    }
}, {
    connection: connection10,
    concurrency: 1
});


worker.on("completed", (job) => {
    logger.debug(`Job completed on queue ${QueueName}`, { jobId: job.id });
});

worker.on("failed", (job, err) => {
    logger.error(`Job failed on queue ${QueueName}`, {
        jobId: job.id,
        jobData: job.data,
        attempts: job.attemptsMade
    }, err);
});

worker.on("error", (err) => {
    logger.error(`Worker error on queue ${QueueName}`, {}, err);
});

worker.on('paused', () => {
    logger.warn(`Worker paused on queue ${QueueName}`);
});

worker.on('resumed', () => {
    logger.info(`Worker resumed on queue ${QueueName}`);
});

// 设置进程错误处理
setupProcessErrorHandlers();

worker.on('closed', () => {
    console.error(`worker ${QueueName} 已退出`);
});

const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            throw new Error(`Process ${type} handled successfully`);
        } catch (error) {
            throw new Error(`Failed to handle process ${type}: ${error.message}`);
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});