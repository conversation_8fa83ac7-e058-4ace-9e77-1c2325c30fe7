const {
    ethers
} = require("ethers");

const moment = require('moment');

const {
    QueuePro,
    WorkerPro
} = require('@taskforcesh/bullmq-pro');

const connection10 = require('../config/redis_config')(10);

const {
    rpcUrl
} = require("../config/rpcUrl_config");

const provider = new ethers.providers.StaticJsonRpcProvider(rpcUrl);

const Redis = require("ioredis");
const redis = new Redis(Object.assign({}, connection10));

const {
    coin
} = require('../config/coin_config');

const queryFilter_Queue = new QueuePro(`${coin}-queryFilter`, {
    connection: connection10
});

const worker = new WorkerPro(`${coin}-block-track`, async (job) => {
    try {
        const num = await provider.getBlockNumber();
        const redisKey = `${coin}-last-db-num`;

        let last_db_num = await redis.get(redisKey);

        if (!last_db_num) {
            await redis.set(redisKey, num);
            last_db_num = num;
        } else {
            // 确保 last_db_num 是数字类型
            last_db_num = parseInt(last_db_num, 10);
            if (isNaN(last_db_num)) {
                console.error(`Invalid last_db_num value: ${last_db_num}, resetting to current block`);
                last_db_num = num;
                await redis.set(redisKey, num);
            }
        }

        const blockDiff = num - last_db_num;

        // 如果区块差距太小，跳过处理
        if (blockDiff < 15) {
            const msg = `区块差距太小: ${blockDiff}`;
            await job.update({
                dbBlockNum: last_db_num,
                chainBlockNumber: num,
                res: msg
            });
            return;
        }

        const msg = `正常处理，区块差距: ${blockDiff}`;
        await job.update({
            dbBlockNum: last_db_num,
            chainBlockNumber: num,
            res: msg
        });

        // 添加查询任务
        await queryFilter_Queue.add(`num_${last_db_num}`, {
            dbBlockNum: last_db_num,
            chainBlockNumber: num
        }, {
            attempts: 10,
            backoff: {
                type: 'exponential',
                delay: 5000,
            },
            removeOnComplete: 1000,
            removeOnFail: 1000
        });

        // 更新处理的区块号
        await redis.incrby(redisKey, 2);

    } catch (error) {
        console.error('Error in block tracking worker:', error);
        throw error; // 重新抛出错误以便BullMQ处理重试
    }
}, {
    connection: connection10
});


worker.on("completed", (job, result) => {
    // console.log(`Completed job on queue trc20_block_track`, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("failed", (job, err) => {
    console.log(`Faille job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});

worker.on("error", (err) => {
    console.log(`error job on queue `, err, moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'));
});


worker.on('closed', () => {
    console.error(`worker ${coin} 已退出`);
});


const errorTypes = ['unhandledRejection', 'uncaughtException']
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2']

errorTypes.forEach(type => {
    process.on(type, async (err, origin) => {
        try {
            console.error(`process.on ${type}`, err, origin);
            await worker.close();
            throw new Error(`Process ${type} handled successfully`);
        } catch (error) {
            throw new Error(`Failed to handle process ${type}: ${error.message}`);
        }
    })
});

signalTraps.forEach(type => {
    process.once(type, async () => {
        console.error('process.once', type);
        try {
            await worker.close();
        } finally {
            process.kill(process.pid, type)
        }
    })
});