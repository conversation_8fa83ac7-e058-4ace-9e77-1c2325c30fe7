const Redis = require('ioredis');
const config = require('./index');

// Redis连接池
const connections = new Map();

// 创建Redis连接
const createRedisConnection = (db = 0) => {
    const key = `redis_${db}`;
    
    if (connections.has(key)) {
        return connections.get(key);
    }

    const redisConfig = Object.assign({}, config.redis, {
        db: db,
        lazyConnect: true,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null
    });

    const redis = new Redis(redisConfig);

    // 错误处理
    redis.on('error', (err) => {
        console.error(`Redis connection error (DB ${db}):`, err);
    });

    redis.on('connect', () => {
        console.log(`Redis connected to DB ${db}`);
    });

    redis.on('ready', () => {
        console.log(`Redis ready (DB ${db})`);
    });

    redis.on('close', () => {
        console.log(`Redis connection closed (DB ${db})`);
    });

    connections.set(key, redis);
    return redis;
};

// 获取特定数据库的Redis连接
const getRedisConnection = (db = 0) => {
    return createRedisConnection(db);
};

// 关闭所有Redis连接
const closeAllConnections = async () => {
    const promises = Array.from(connections.values()).map(redis => {
        return redis.disconnect();
    });
    
    await Promise.all(promises);
    connections.clear();
    console.log('All Redis connections closed');
};

// 处理进程退出
process.on('SIGINT', closeAllConnections);
process.on('SIGTERM', closeAllConnections);

module.exports = {
    getRedisConnection,
    createRedisConnection,
    closeAllConnections
};
