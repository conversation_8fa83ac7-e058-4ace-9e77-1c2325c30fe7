const mysqlConfig = require('./mysqlConfig');

// 创建数据库连接配置
const createKnexConfig = (poolConfig = {}) => {
    const defaultPoolConfig = {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        createTimeoutMillis: 30000,
        destroyTimeoutMillis: 5000,
        idleTimeoutMillis: 30000,
        reapIntervalMillis: 1000,
        createRetryIntervalMillis: 100
    };

    return {
        client: 'mysql2',
        connection: {
            host: mysqlConfig.host,
            user: mysqlConfig.user,
            port: mysqlConfig.port,
            password: mysqlConfig.password,
            database: mysqlConfig.database,
            charset: 'utf8mb4',
            timezone: '+08:00',
            supportBigNumbers: true,
            bigNumberStrings: true
        },
        pool: Object.assign({}, defaultPoolConfig, poolConfig),
        acquireConnectionTimeout: 30000,
        migrations: {
            tableName: 'knex_migrations'
        }
    };
};

// 创建不同用途的数据库实例
const knex = require('knex');

// 主数据库连接（用于高并发场景）
const mainDb = knex(createKnexConfig({
    min: 5,
    max: 20
}));

// 轻量级数据库连接（用于低并发场景）
const lightDb = knex(createKnexConfig({
    min: 1,
    max: 5
}));

// 优雅关闭数据库连接
const closeConnections = async () => {
    try {
        await mainDb.destroy();
        await lightDb.destroy();
        console.log('Database connections closed successfully');
    } catch (error) {
        console.error('Error closing database connections:', error);
    }
};

// 处理进程退出
process.on('SIGINT', closeConnections);
process.on('SIGTERM', closeConnections);

module.exports = {
    mainDb,
    lightDb,
    createKnexConfig,
    closeConnections
};
