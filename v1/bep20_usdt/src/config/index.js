const path = require('path');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config({
    path: path.join(__dirname, '../../.env')
});

// 验证必需的环境变量
const requiredEnvVars = [
    'mysql_host',
    'mysql_user', 
    'mysql_password',
    'mysql_database',
    'redis_host',
    'redis_port'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
    const errorMessage = `Missing required environment variables: ${missingVars.join(', ')}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
}

// 配置对象
const config = {
    // 应用配置
    app: {
        name: 'bep20-usdt-tracker',
        version: '1.0.0',
        env: process.env.NODE_ENV || 'development',
        port: parseInt(process.env.PORT) || 8117
    },

    // 数据库配置
    database: {
        host: process.env.mysql_host,
        user: process.env.mysql_user,
        port: parseInt(process.env.mysql_port) || 3306,
        password: process.env.mysql_password,
        database: process.env.mysql_database,
        charset: 'utf8mb4',
        timezone: '+08:00'
    },

    // Redis配置
    redis: {
        host: process.env.redis_host,
        port: parseInt(process.env.redis_port) || 6379,
        password: process.env.redis_password || undefined,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null
    },

    // 区块链配置
    blockchain: {
        rpcUrl: process.env.BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
        chainId: 'bsc',
        blockConfirmations: 15
    },

    // 安全配置
    security: {
        adminUsername: process.env.ADMIN_USERNAME || 'admin',
        adminPassword: process.env.ADMIN_PASSWORD,
        sessionSecret: process.env.SESSION_SECRET || require('crypto').randomBytes(32).toString('hex'),
        sessionMaxAge: 24 * 60 * 60 * 1000 // 24小时
    },

    // 队列配置
    queue: {
        defaultAttempts: 10,
        defaultBackoffDelay: 5000,
        removeOnComplete: 1000,
        removeOnFail: 1000,
        concurrency: {
            blockTrack: 1,
            queryFilter: 1,
            checkLogs: 50,
            updateBalance: 1
        }
    },

    // 币种配置
    coin: {
        name: 'bep20-usdt',
        symbol: 'USDT',
        decimals: 18,
        contractAddress: '******************************************'
    }
};

// 验证关键配置
if (!config.security.adminPassword) {
    console.warn('Warning: ADMIN_PASSWORD not set, using default credentials');
}

// 导出配置
module.exports = config;
