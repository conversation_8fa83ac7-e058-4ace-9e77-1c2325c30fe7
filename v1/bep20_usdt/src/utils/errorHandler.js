const { createLogger } = require('./logger');

const logger = createLogger('ERROR_HANDLER');

// 自定义错误类
class AppError extends Error {
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.timestamp = new Date().toISOString();
        
        Error.captureStackTrace(this, this.constructor);
    }
}

class DatabaseError extends AppError {
    constructor(message, originalError = null) {
        super(`Database Error: ${message}`, 500);
        this.originalError = originalError;
        this.type = 'DATABASE_ERROR';
    }
}

class BlockchainError extends AppError {
    constructor(message, originalError = null) {
        super(`Blockchain Error: ${message}`, 500);
        this.originalError = originalError;
        this.type = 'BLOCKCHAIN_ERROR';
    }
}

class RedisError extends AppError {
    constructor(message, originalError = null) {
        super(`Redis Error: ${message}`, 500);
        this.originalError = originalError;
        this.type = 'REDIS_ERROR';
    }
}

class ValidationError extends AppError {
    constructor(message) {
        super(`Validation Error: ${message}`, 400);
        this.type = 'VALIDATION_ERROR';
    }
}

// 错误处理函数
const handleError = (error, context = '') => {
    const errorInfo = {
        message: error.message,
        stack: error.stack,
        context: context,
        timestamp: new Date().toISOString()
    };

    if (error instanceof AppError) {
        logger.error(`${error.type}: ${error.message}`, errorInfo, error.originalError);
    } else {
        logger.error(`Unhandled Error: ${error.message}`, errorInfo, error);
    }

    return error;
};

// 异步错误包装器
const asyncErrorHandler = (fn) => {
    return async (...args) => {
        try {
            return await fn(...args);
        } catch (error) {
            throw handleError(error, fn.name);
        }
    };
};

// 数据库操作错误处理
const handleDatabaseOperation = async (operation, context = '') => {
    try {
        return await operation();
    } catch (error) {
        throw new DatabaseError(`${context}: ${error.message}`, error);
    }
};

// 区块链操作错误处理
const handleBlockchainOperation = async (operation, context = '') => {
    try {
        return await operation();
    } catch (error) {
        throw new BlockchainError(`${context}: ${error.message}`, error);
    }
};

// Redis操作错误处理
const handleRedisOperation = async (operation, context = '') => {
    try {
        return await operation();
    } catch (error) {
        throw new RedisError(`${context}: ${error.message}`, error);
    }
};

// 验证函数
const validateRequired = (data, requiredFields) => {
    const missing = requiredFields.filter(field => !data[field]);
    if (missing.length > 0) {
        throw new ValidationError(`Missing required fields: ${missing.join(', ')}`);
    }
};

const validateAddress = (address) => {
    if (!address || typeof address !== 'string' || !/^0x[a-fA-F0-9]{40}$/.test(address)) {
        throw new ValidationError(`Invalid Ethereum address: ${address}`);
    }
};

const validateTransactionHash = (hash) => {
    if (!hash || typeof hash !== 'string' || !/^0x[a-fA-F0-9]{64}$/.test(hash)) {
        throw new ValidationError(`Invalid transaction hash: ${hash}`);
    }
};

// 进程错误处理
const setupProcessErrorHandlers = () => {
    process.on('uncaughtException', (error) => {
        logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
        throw new AppError('Uncaught Exception occurred', 500, true, error.stack);
    });

    process.on('unhandledRejection', (reason, promise) => {
        logger.error('Unhandled Rejection', { 
            reason: reason instanceof Error ? reason.message : reason,
            stack: reason instanceof Error ? reason.stack : undefined,
            promise: promise.toString()
        });
    });

    process.on('SIGTERM', () => {
        logger.info('SIGTERM received, shutting down gracefully');
        throw new AppError('SIGTERM signal received - graceful shutdown', 0, false);
    });

    process.on('SIGINT', () => {
        logger.info('SIGINT received, shutting down gracefully');
        throw new AppError('SIGINT signal received - graceful shutdown', 0, false);
    });
};

module.exports = {
    AppError,
    DatabaseError,
    BlockchainError,
    RedisError,
    ValidationError,
    handleError,
    asyncErrorHandler,
    handleDatabaseOperation,
    handleBlockchainOperation,
    handleRedisOperation,
    validateRequired,
    validateAddress,
    validateTransactionHash,
    setupProcessErrorHandlers
};
